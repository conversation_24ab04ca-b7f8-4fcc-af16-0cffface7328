package open

import (
	"app_service/apps/business/card_community/api/open"

	"github.com/gin-gonic/gin"
)

// OrderTimeoutRouter 订单超时处理路由
func OrderTimeoutRouter(r *gin.RouterGroup) {
	cardCommunityGroup := r.Group("/card_community")
	{
		ordersGroup := cardCommunityGroup.Group("/orders")
		{
			timeoutGroup := ordersGroup.Group("/timeout")
			{
				timeoutGroup.POST("/process", open.ProcessTimeoutOrders) // 处理超时订单
			}
		}
	}
}

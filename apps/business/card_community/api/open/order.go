package open

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// CardOrderTimeoutClose
// @Summary 卡牌订单支付超时关闭
// @Description 卡牌订单支付超时关闭
// @Tags open端-卡牌订单
// @x-apifox-folder "open端/卡牌订单"
// @Param data body define.CardOrderTimeoutCloseReq true "请求参数"
// @Success 200 {object} response.Data{}
// @Router /open/v1/card_community/orders/timeout_close [post]
// @Security Bearer
// @produce  json
func CardOrderTimeoutClose(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CardOrderTimeoutCloseReq{}, s.CardOrderTimeoutClose)
}

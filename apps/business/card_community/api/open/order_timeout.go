package open

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"
	"app_service/pkg/gin_request_process"

	"github.com/gin-gonic/gin"
)

// ProcessTimeoutOrders
// @Summary 处理超时订单
// @Description 查询过期的未支付订单并批量处理，将其状态更新为已取消
// @Tags 开放接口-卡牌社区
// @x-apifox-folder "开放接口/卡牌社区/订单超时处理"
// @Param data body define.OrderTimeoutProcessRequest true "订单超时处理参数"
// @Success 200 {object} response.Data{data=define.OrderTimeoutProcessResponse}
// @Router /open/v1/card_community/orders/timeout/process [post]
func ProcessTimeoutOrders(ctx *gin.Context) {
	s := service.NewOrderTimeoutOpenService(ctx)
	gin_request_process.MainHandle(ctx, &define.OrderTimeoutProcessRequest{}, s.ProcessTimeoutOrders)
}

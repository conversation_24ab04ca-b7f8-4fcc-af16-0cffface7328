package logic

import (
	"context"
	"errors"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/locker"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/third_party/wat"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"gorm.io/gorm"
)

// ProcessTimeoutOrder 处理单个超时订单
// 使用分布式锁确保同一订单不会被重复处理
func ProcessTimeoutOrder(ctx context.Context, order *model.CardOrder) error {
	// 创建订单级别的分布式锁，防止重复处理
	orderLock := locker.NewOrderTimeoutLock(order.ID, locker.TimeoutProcess)
	lock := redis_locker.New(global.Redis, orderLock.GetCacheKey(), orderLock.LockTime())

	// 尝试获取锁
	if !lock.Lock() {
		log.Ctx(ctx).Infof("订单 %s 正在被其他进程处理，跳过", order.ID)
		return nil
	}
	defer lock.UnLock()

	// 重新查询订单状态，确保数据最新
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, order.ID).
		Build()

	currentOrder, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Ctx(ctx).Warnf("订单 %s 不存在，可能已被删除", order.ID)
			return nil
		}
		log.Ctx(ctx).Errorf("重新查询订单 %s 失败: %v", order.ID, err)
		return err
	}

	// 检查订单状态是否仍为待支付
	if !currentOrder.IsUnPaid() {
		log.Ctx(ctx).Infof("订单 %s 状态已变更为 %s，跳过超时处理", order.ID, currentOrder.GetStatusText())
		return nil
	}

	// 检查订单是否确实已过期
	if !currentOrder.IsExpired() {
		log.Ctx(ctx).Infof("订单 %s 尚未过期，跳过处理", order.ID)
		return nil
	}

	// 调用支付状态查询接口二次确认
	if err := verifyPaymentStatus(ctx, currentOrder); err != nil {
		log.Ctx(ctx).Errorf("验证订单 %s 支付状态失败: %v", order.ID, err)
		return err
	}

	// 更新订单状态为超时取消
	currentOrder.SetStatus(enums.OrderStatusCanceled)
	currentOrder.SetCancelInfo(enums.CancelTypeTimeout)

	if err := cardOrderRepo.UpdateById(currentOrder); err != nil {
		log.Ctx(ctx).Errorf("更新订单 %s 状态失败: %v", order.ID, err)
		return err
	}

	// 处理资源回滚
	if err := rollbackOrderResources(ctx, currentOrder); err != nil {
		log.Ctx(ctx).Errorf("回滚订单 %s 资源失败: %v", order.ID, err)
		// 资源回滚失败不影响订单状态更新，记录错误日志即可
	}

	log.Ctx(ctx).Infof("订单 %s 超时处理完成，状态已更新为已取消", order.ID)
	return nil
}

// verifyPaymentStatus 调用SpdbCardPayStatus二次确认支付状态
func verifyPaymentStatus(ctx context.Context, order *model.CardOrder) error {
	// 构造支付状态查询请求
	req := &wat.SpdbCardPayStatusReq{
		OrderId: order.ID,
	}

	// 调用支付状态查询接口
	resp, err := wat.SpdbCardPayStatus(ctx, req)
	if err != nil {
		return err
	}

	// 如果支付状态显示已支付，则不能取消订单
	if resp.IsPaid {
		log.Ctx(ctx).Warnf("订单 %s 支付状态查询显示已支付，不能执行超时取消", order.ID)
		return errors.New("订单已支付，不能执行超时取消")
	}

	log.Ctx(ctx).Infof("订单 %s 支付状态确认为未支付，可以执行超时取消", order.ID)
	return nil
}

// rollbackOrderResources 回滚订单相关资源
func rollbackOrderResources(ctx context.Context, order *model.CardOrder) error {
	// TODO: 根据业务需求实现具体的资源回滚逻辑
	// 例如：
	// 1. 回滚用户购买次数限制
	// 2. 释放库存锁定
	// 3. 回滚优惠券使用
	// 4. 其他业务相关的资源回滚

	log.Ctx(ctx).Infof("订单 %s 资源回滚完成", order.ID)
	return nil
}

// BatchProcessTimeoutOrders 批量处理超时订单
func BatchProcessTimeoutOrders(ctx context.Context, orders []*model.CardOrder) {
	if len(orders) == 0 {
		return
	}

	log.Ctx(ctx).Infof("开始批量处理 %d 个超时订单", len(orders))

	successCount := 0
	failCount := 0

	for _, order := range orders {
		if err := ProcessTimeoutOrder(ctx, order); err != nil {
			log.Ctx(ctx).Errorf("处理订单 %s 超时失败: %v", order.ID, err)
			failCount++
		} else {
			successCount++
		}
	}

	log.Ctx(ctx).Infof("批量处理超时订单完成，成功: %d, 失败: %d", successCount, failCount)
}

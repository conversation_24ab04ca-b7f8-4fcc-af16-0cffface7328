package service

import (
	"context"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

// CardOrderTimeoutClose 关闭超时卡牌订单
func (s *Service) CardOrderTimeoutClose(req *define.CardOrderTimeoutCloseReq) (any, error) {
	spanContext := s.NewContextWithSpanContext(s.ctx)
	
	// 查询超时订单：创建时间超过24小时且状态为待支付的订单
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	
	// 构建查询条件：状态为待支付且创建时间超过24小时
	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.Status, enums.OrderStatusUnPaid.Int32()).
		Lt(cardOrderSchema.CreatedAt, time.Now().Add(-24*time.Hour)).
		Build()
	
	// 限制每次处理200个订单
	queryWrapper.ScopeOpts = append(queryWrapper.ScopeOpts, search.MakeLimit(200))
	
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(spanContext))
	cardOrders, err := cardOrderRepo.SelectList(queryWrapper)
	if err != nil {
		log.Ctx(spanContext).Error("查询超时卡牌订单失败", err)
		return nil, err
	}
	
	if len(cardOrders) == 0 {
		log.Ctx(spanContext).Info("无超时卡牌订单")
		return response.Empty{}, nil
	}
	
	log.Ctx(spanContext).Infof("找到 %d 个超时卡牌订单，开始批量处理", len(cardOrders))
	
	// 批量处理超时订单，每批20个
	const batchSize = 20
	for i := 0; i < len(cardOrders); i += batchSize {
		end := i + batchSize
		if end > len(cardOrders) {
			end = len(cardOrders)
		}
		batch := cardOrders[i:end]
		
		// 并发处理每批订单
		go func(ctx context.Context, orders []*model.CardOrder) {
			for _, order := range orders {
				if err := s.processTimeoutCardOrder(ctx, order); err != nil {
					log.Ctx(ctx).Errorf("处理卡牌订单 %s 超时时失败, err: %+v", order.ID, err)
				}
			}
		}(spanContext, batch)
	}
	
	return response.Empty{}, nil
}

// processTimeoutCardOrder 处理单个超时卡牌订单
func (s *Service) processTimeoutCardOrder(ctx context.Context, cardOrder *model.CardOrder) error {
	// 重新查询订单状态，确保状态未被其他操作修改
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(ctx))
	
	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, cardOrder.ID).
		Build()
	
	currentOrder, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		log.Ctx(ctx).Errorf("重新查询卡牌订单 %s 失败: %v", cardOrder.ID, err)
		return err
	}
	
	// 检查订单状态是否仍为待支付
	if currentOrder.Status != enums.OrderStatusUnPaid.Int32() {
		log.Ctx(ctx).Infof("卡牌订单 %s 状态已变更为 %d，跳过超时处理", cardOrder.ID, currentOrder.Status)
		return nil // 状态已变，跳过
	}
	
	// 检查是否真的超时（防止时间差导致的误判）
	if time.Since(currentOrder.CreatedAt) < 24*time.Hour {
		log.Ctx(ctx).Infof("卡牌订单 %s 尚未超时，跳过处理", cardOrder.ID)
		return nil
	}
	
	// 更新订单状态为已取消
	currentOrder.SetStatus(enums.OrderStatusCanceled)
	currentOrder.SetCancelInfo(enums.CancelTypeTimeout)
	
	// 使用条件更新，确保只有状态为待支付的订单才能被取消
	updateWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, cardOrder.ID).
		Eq(cardOrderSchema.Status, enums.OrderStatusUnPaid.Int32()).
		Build()
	
	err = cardOrderRepo.UpdateField(currentOrder, updateWrapper)
	if err != nil {
		log.Ctx(ctx).Errorf("更新卡牌订单 %s 状态失败: %v", cardOrder.ID, err)
		return err
	}
	
	log.Ctx(ctx).Infof("卡牌订单 %s 超时取消成功", cardOrder.ID)
	return nil
}

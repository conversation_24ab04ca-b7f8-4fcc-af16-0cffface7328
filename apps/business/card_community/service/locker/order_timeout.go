package locker

import (
	"fmt"
	"time"
)

// OrderTimeoutAction 订单超时处理相关行为枚举
type OrderTimeoutAction string

const (
	TimeoutProcess OrderTimeoutAction = "timeout_process" // 超时处理
)

type OrderTimeoutLock struct {
	ac  OrderTimeoutAction // 行为
	tag string             // 唯一标识
}

func (p *OrderTimeoutLock) GetCacheKey() string {
	return fmt.Sprintf("app_service:card_community:order_timeout:locker:%s:%s", p.ac, p.tag)
}

func (p *OrderTimeoutLock) LockTime() time.Duration {
	return time.Second * 30 // 30秒锁定时间，确保定时任务不重复执行
}

func NewOrderTimeoutLock(tag string, ac OrderTimeoutAction) *OrderTimeoutLock {
	return &OrderTimeoutLock{tag: tag, ac: ac}
}

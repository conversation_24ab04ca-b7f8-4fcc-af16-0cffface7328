package service

import (
	"context"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/locker"
	"app_service/apps/business/card_community/service/logic"
	"app_service/global"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"github.com/gin-gonic/gin"
)

// OrderTimeoutOpenService 订单超时处理开放接口服务
type OrderTimeoutOpenService struct {
	ctx context.Context
}

// NewOrderTimeoutOpenService 创建订单超时处理开放接口服务实例
func NewOrderTimeoutOpenService(ctx *gin.Context) *OrderTimeoutOpenService {
	return &OrderTimeoutOpenService{
		ctx: ctx,
	}
}

// ProcessTimeoutOrders 处理超时订单
// 查询过期的未支付订单并批量处理
func (s *OrderTimeoutOpenService) ProcessTimeoutOrders(req *define.OrderTimeoutProcessRequest) (*define.OrderTimeoutProcessResponse, error) {
	// 使用全局分布式锁，防止多个定时任务同时执行
	globalLock := locker.NewOrderTimeoutLock("global", locker.TimeoutProcess)
	lock := redis_locker.New(global.Redis, globalLock.GetCacheKey(), globalLock.LockTime())

	if !lock.Lock() {
		log.Ctx(s.ctx).Info("订单超时处理任务正在执行中，跳过本次调度")
		return &define.OrderTimeoutProcessResponse{
			ProcessedCount: 0,
			Message:        "任务正在执行中，跳过本次调度",
		}, nil
	}
	defer lock.UnLock()

	// 设置默认批处理大小
	batchSize := req.BatchSize
	if batchSize <= 0 || batchSize > 50 {
		batchSize = 20 // 默认每批处理20个订单
	}

	// 查询超时的未支付订单
	timeoutOrders, err := s.queryTimeoutOrders(batchSize)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询超时订单失败: %v", err)
		return nil, err
	}

	if len(timeoutOrders) == 0 {
		log.Ctx(s.ctx).Info("没有找到需要处理的超时订单")
		return &define.OrderTimeoutProcessResponse{
			ProcessedCount: 0,
			Message:        "没有找到需要处理的超时订单",
		}, nil
	}

	log.Ctx(s.ctx).Infof("找到 %d 个超时订单，开始处理", len(timeoutOrders))

	// 批量处理超时订单
	logic.BatchProcessTimeoutOrders(s.ctx, timeoutOrders)

	return &define.OrderTimeoutProcessResponse{
		ProcessedCount: len(timeoutOrders),
		Message:        "超时订单处理完成",
	}, nil
}

// queryTimeoutOrders 查询超时的未支付订单
func (s *OrderTimeoutOpenService) queryTimeoutOrders(limit int) ([]*model.CardOrder, error) {
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	// 构建查询条件：状态为待支付且过期时间小于当前时间
	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.Status, enums.OrderStatusUnPaid.Int32()).
		Lt(cardOrderSchema.ExpiredAt, time.Now()).
		OrderBy(cardOrderSchema.CreatedAt, search.ASC). // 按创建时间升序，优先处理较早的订单
		Limit(limit).
		Build()

	orders, err := cardOrderRepo.SelectList(queryWrapper)
	if err != nil {
		return nil, err
	}

	log.Ctx(s.ctx).Infof("查询到 %d 个超时未支付订单", len(orders))
	return orders, nil
}

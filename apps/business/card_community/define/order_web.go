package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 创建订单相关结构体
type (
	// CreateOrderRequest 创建订单请求
	CreateOrderRequest struct {
		BuyerID         string     `json:"buyer_id" binding:"required"`                // 买家ID
		CardItems       []CardItem `json:"card_items" binding:"required,min=1"`        // 卡片信息
		CardGroupsCount int        `json:"card_groups_count" binding:"required,min=1"` // 卡片组数
		TotalQuantity   int        `json:"total_quantity" binding:"required,min=1"`    // 卡片总数量
		TotalAmount     int64      `json:"total_amount" binding:"required,min=1"`      // 总金额(分)
	}

	// CreateOrderResponse 创建订单响应
	CreateOrderResponse struct {
		OrderID string `json:"order_id"` // 订单ID
	}
)

// 订单支付相关结构体
type (
	// PayOrderRequest 支付订单请求
	PayOrderRequest struct {
		OrderID         string  `json:"order_id" binding:"required"`         // 订单ID
		ShippingAddress Address `json:"shipping_address" binding:"required"` // 收货地址
		PayPwd          string  `json:"pay_pwd" binding:"required"`          // 支付密码
	}

	// PayOrderResponse 支付订单响应
	PayOrderResponse struct {
		OrderID string `json:"order_id"` // 订单ID
	}
)

// 订单详情相关结构体
type (
	// GetOrderDetailRequest 获取订单详情请求
	GetOrderDetailRequest struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 订单ID
	}

	// OrderDetailResponse 订单详情响应
	OrderDetailResponse struct {
		OrderID         string              `json:"order_id"`          // 订单ID（同时作为订单号）
		BuyerID         string              `json:"buyer_id"`          // 买家ID
		BuyerInfo       UserInfo            `json:"buyer_info"`        // 买家信息（包含昵称和头像）
		SellerID        string              `json:"seller_id"`         // 卖家ID
		SellerInfo      UserInfo            `json:"seller_info"`       // 卖家信息（包含昵称和头像）
		CardItems       []CardItem          `json:"card_items"`        // 卡片信息
		CardGroupsCount int                 `json:"card_groups_count"` // 卡片组数
		TotalQuantity   int                 `json:"total_quantity"`    // 卡片总数量
		TotalAmount     int64               `json:"total_amount"`      // 订单总金额(分)
		PayAmount       int64               `json:"pay_amount"`        // 支付金额(分)
		PaymentMethod   enums.PaymentMethod `json:"payment_method"`    // 支付方式: 1=卡牌钱包
		Status          enums.OrderStatus   `json:"status"`            // 订单状态: 0=待支付, 10=待发货, 20=待收货, 30=已完成, 40=已取消
		CancelType      enums.CancelType    `json:"cancel_type"`       // 取消类型: 1=买家取消, 2=卖家取消, 3=超时取消
		ShippingAddress *Address            `json:"shipping_address"`  // 收货地址
		PaymentAt       *time.Time          `json:"payment_at"`        // 支付时间
		DeliveredAt     *time.Time          `json:"delivered_at"`      // 发货时间
		ReceivedAt      *time.Time          `json:"received_at"`       // 收货时间(成交时间）
		ExpiredAt       time.Time           `json:"expired_at"`        // 订单过期时间
		CreatedAt       time.Time           `json:"created_at"`        // 创建时间
	}
)

// 订单列表相关结构体
type (
	// GetOrderListRequest 获取订单列表请求
	GetOrderListRequest struct {
		pagination.Pagination
		UserType enums.UserType     `form:"user_type" json:"user_type" binding:"required,oneof=1 2"` // 用户类型: 1=买家, 2=卖家
		Status   *enums.OrderStatus `form:"status" json:"status"`                                    // 订单状态筛选，可选: 0=待支付, 10=待发货, 20=待收货, 30=已完成, 40=已取消
	}

	// OrderListItem 订单列表项
	OrderListItem struct {
		OrderID           string            `json:"order_id"`             // 订单ID
		OtherPartyID      string            `json:"other_party_id"`       // 对方ID（买家视角显示卖家ID，卖家视角显示买家ID）
		OtherPartyInfo    UserInfo          `json:"other_party_info"`     // 对方信息（包含昵称和头像）
		FirstCardImageURL string            `json:"first_card_image_url"` // 第一张卡牌图片URL
		CardGroupsCount   int               `json:"card_groups_count"`    // 卡片组数
		TotalQuantity     int               `json:"total_quantity"`       // 卡片总数量
		TotalAmount       int64             `json:"total_amount"`         // 订单总金额(分)
		Status            enums.OrderStatus `json:"status"`               // 订单状态: 0=待支付, 10=待发货, 20=待收货, 30=已完成, 40=已取消
		CreatedAt         time.Time         `json:"created_at"`           // 创建时间
	}

	// GetOrderListResponse 获取订单列表响应
	GetOrderListResponse struct {
		List    []*OrderListItem `json:"list"`     // 订单列表
		HasMore bool             `json:"has_more"` // 是否有更多
	}
)

// 订单状态统计相关结构体
type (
	// GetOrderStatsRequest 获取订单状态统计请求
	GetOrderStatsRequest struct {
	}

	// OrderStatusStats 订单状态统计
	OrderStatusStats struct {
		UnPaidCount      int64 `json:"unpaid_count"`      // 待支付订单数
		UnDeliveredCount int64 `json:"undelivered_count"` // 待发货订单数
		UnReceiveCount   int64 `json:"unreceive_count"`   // 待收货订单数
	}

	// OrderStatsResponse 订单统计响应（支持分别返回买家和卖家统计）
	OrderStatsResponse struct {
		BuyerStats  *OrderStatusStats `json:"buyer_stats,omitempty"`  // 买家订单统计
		SellerStats *OrderStatusStats `json:"seller_stats,omitempty"` // 卖家订单统计
	}
)

// 订单操作相关结构体
type (
	// CancelOrderRequest 取消订单请求
	CancelOrderRequest struct {
		OrderID string `json:"order_id" binding:"required"` // 订单ID
	}

	// DeleteOrderRequest 删除订单请求
	DeleteOrderRequest struct {
		OrderID string `json:"order_id" binding:"required"` // 订单ID
	}

	// ShipOrderRequest 卖家发货请求
	ShipOrderRequest struct {
		OrderID string `json:"order_id" binding:"required"` // 订单ID
	}

	// ConfirmOrderRequest 买家确认收货请求
	ConfirmOrderRequest struct {
		OrderID string `json:"order_id" binding:"required"` // 订单ID
	}

	// OrderOperationResponse 订单操作响应
	OrderOperationResponse struct {
		OrderID string `json:"order_id"` // 订单ID
	}
)

// 订单校验相关结构体
type (
	// CreateValidateOrderRequest 订单创建校验请求
	CreateValidateOrderRequest struct {
		BuyerID string `json:"buyer_id" binding:"required"` // 买家ID
	}

	// CreateValidateOrderResponse 订单创建校验响应
	CreateValidateOrderResponse struct {
	}
)

package web

import (
	"app_service/apps/business/card_community/api/web"

	"github.com/gin-gonic/gin"
)

// Order 注册订单相关路由
func Order(router *gin.RouterGroup) {
	// 订单管理相关路由
	orderGroup := router.Group("card_community/orders")
	{
		// 创建订单
		orderGroup.POST("/create", web.CreateOrder)
		// 订单创建前置校验
		orderGroup.POST("/create_validate", web.CreateValidateOrder)

		// 订单查询
		orderGroup.GET("/detail", web.GetOrderDetail)
		orderGroup.GET("/list", web.GetOrderList) // 统一订单列表接口

		// 订单状态统计
		orderGroup.GET("/stats", web.GetOrderStats) // 统一订单统计接口

		// 订单操作
		orderGroup.POST("/pay", web.PayOrder)
		orderGroup.POST("/cancel", web.CancelOrder)
		orderGroup.POST("/delete", web.DeleteOrder)
		orderGroup.POST("/ship", web.ShipOrder)
		orderGroup.POST("/confirm", web.ConfirmReceived)
	}
}

package router

import (
	"app_service/apps/business/card_community/router/admin"
	"app_service/apps/business/card_community/router/web"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"
	"fmt"

	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}

func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 客户端路由
	webRoute(r)
	// 管理端路由
	adminRoute(r)
	// 开放端路由
	openRoute(r)
}

// 客户端路由
func webRoute(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			// 帖子列表
			"/web/v1/posts/list",
			// 帖子详情
			"/web/v1/posts/detail",
		},
	}))

	// 注册各个模块的路由
	web.Conversation(w)        // 会话管理
	web.Message(w)             // 消息管理
	web.Post(w)                // 帖子管理
	web.MerchantApplication(w) // 商家申请
	web.SmartReply(w)          // 智能回复
	web.Order(w)               // 订单管理
}

// 管理端路由
func adminRoute(router *gin.Engine) {
	a := router.Group("/admin/v1", middlewares.ParseHead)
	a.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery()) //auth.Auth(&auth.Admin{
	//	NoAuthUrl: []string{},
	//})

	// 注册各个模块的路由
	admin.Conversation(a) // 会话管理
	admin.Message(a)      // 消息管理
	admin.Post(a)         // 帖子管理
	admin.Merchant(a)     // 商家申请管理
	admin.Order(a)        // 订单管理
}

// 开放端路由
func openRoute(router *gin.Engine) {
	o := router.Group("/open/v1", middlewares.ParseHead)
	o.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token: global.GlobalConfig.Service.Token,
	}))

	// 注册各个模块的路由
	open.Order(o) // 订单管理
}

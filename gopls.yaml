build:
  # 排除不需要分析的目录
  directoryFilters:
    - "-node_modules"
    - "-logs"
    - "-assets"
    - "-docs"
  # 构建标签
  buildFlags: []
  # 环境变量
  env:
    GOPROXY: "https://goproxy.cn,direct"
    GOSUMDB: "sum.golang.google.cn"

ui:
  # 代码补全设置
  completion:
    usePlaceholders: true
    completionBudget: "100ms"
    matcher: "Fuzzy"
  
  # 诊断设置
  diagnostic:
    staticcheck: true
    analyses:
      unusedparams: true
      shadow: true
      
  # 导航设置
  navigation:
    importShortcut: "Both"
    symbolMatcher: "Fuzzy"
    symbolStyle: "Dynamic"
  
  # 语义标记
  semanticTokens: true
  
  # 代码镜头
  codelenses:
    gc_details: false
    generate: true
    regenerate_cgo: true
    test: true
    tidy: true
    upgrade_dependency: true
    vendor: true

# 格式化设置
formatting:
  gofumpt: true
  local: "app_service"

# 链接器设置
linksInHover: true
linkTarget: "pkg.go.dev"

# 实验性功能
experimentalPostfixCompletions: true
experimentalWorkspaceModule: true

# 项目上下文信息
## 具体的项目实现记录和业务上下文

- GenerateID函数提供接口给前端调用，用于生成Snowflake ID
- 缓存防护功能已完成实现：1)随机TTL防雪崩-帖子详情和列表缓存使用基础10分钟+0-10%随机时间 2)空值缓存防穿透-仅帖子详情使用2分钟空值缓存，列表不使用 3)新增pkg/cache/cache_util.go通用缓存工具类 4)修改apps/business/card_community/service/logic/post.go和post_web.go实现新缓存逻辑 5)提供test_cache_simple.sh和test_api_cache.sh测试脚本
- 帖子管理业务逻辑已完成重构：1. 主要业务逻辑在 service 层实现（post_web.go, post_admin.go）2. 复用逻辑在 logic 层（merchant_permission.go）3. 使用 UserInfo 结构体统一用户信息格式 4. 通过 pat.GetRealInfo 获取真实用户信息（昵称、头像）5. API 层直接调用具体服务类，避免中间层
- 消息发送频率限制规则：用户向某个接收方连续发送超过5条消息且对方未回复时，提示"你已向 Ta 发送 5 条消息，请耐心等待回复～"并阻止发送；对方回复后重置计数；不考虑时间窗口和消息类型
- 订单列表接口优化完成：1.新增统一的GetOrderList接口，通过UserType参数区分买家(1)和卖家(2)视角；2.GetOrderListRequest增加UserType字段，支持user_type参数；4.新增路由/web/v1/orders/list作为统一入口；5.大幅减少重复代码，提高维护性
- 订单创建业务规则实现：1.修改FindExistingConversation返回*model.Conversation而非字符串；2.新增checkSellerUnpaidOrderLimit校验卖家待支付订单不超过5单；3.新增checkConversationStatus校验会话状态，会话不存在或状态为-1时不能创建订单；4.在CreateOrder中按顺序执行校验：买家有效性->卖家订单限制->会话状态->订单创建


{"folders": [{"path": "."}], "settings": {"go.useLanguageServer": true, "go.languageServerExperimentalFeatures": {"diagnostics": true, "documentLink": true}, "go.toolsManagement.autoUpdate": true, "go.gotoSymbol.includeImports": true, "go.gotoSymbol.includeGoroot": true, "go.inferGopath": true, "gopls": {"ui.completion.usePlaceholders": true, "ui.diagnostic.staticcheck": true, "ui.completion.completionBudget": "100ms", "ui.completion.matcher": "Fuzzy", "ui.navigation.importShortcut": "Both", "ui.semanticTokens": true, "formatting.gofumpt": true, "formatting.local": "app_service", "build.directoryFilters": ["-node_modules", "-logs"]}, "files.exclude": {"**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/logs": true}}, "extensions": {"recommendations": ["golang.go"]}}